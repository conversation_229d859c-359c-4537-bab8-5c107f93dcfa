﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Graduation.Core.Resources
{
    public static class SharedResourcesKeys
    {
        public const string Required = "Required";
        public const string NotFound = "NotFound";
        public const string Deleted = "Deleted";
        public const string Created = "Created";
        public const string Success = "Success";
        public const string NotEmpty = "NotEmpty";
        public const string Updated = "Updated";
        public const string UnAuthorized = "UnAuthorized";
        public const string BadRequest = "BadRequest";
        public const string UnprocessableEntity = "UnprocessableEntity";
        public const string MaxLengthis100 = "MaxLengthis100";
        public const string IsExist = "IsExist";
        public const string IsNotExist = "IsNotExist";
        public const string DepartmementId = "DepartmementId";
        public const string PasswordNotEqualConfirmPass = "PasswordNotEqualConfirmPass";
        public const string EmailIsExist = "EmailIsExist";
        public const string UserNameIsExist = "UserNameIsExist";
        public const string FaildToAddUser = "FaildToAddUser";
        public const string UpdateFailed = "UpdateFailed";
        public const string DeletedFailed = "DeletedFailed";
        public const string ChangePassFailed = "ChangePassFailed";
        public const string UserName = "UserName";
        public const string Password = "Password";
        public const string UserNameIsNotExist = "UserNameIsNotExist";
        public const string PasswordNotCorrect = "PasswordNotCorrect";
        public const string AlgorithmIsWrong = "AlgorithmIsWrong";
        public const string TokenIsNotExpired = "TokenIsNotExpired";
        public const string RefreshTokenIsNotFound = "RefreshTokenIsNotFound";
        public const string RefreshTokenIsExpired = "RefreshTokenIsExpired";
        public const string TokenIsExpired = "TokenIsExpired";
        public const string AddFailed = "AddFailed";
        public const string RoleNotExist = "RoleNotExist";
        public const string RoleIsUsed = "RoleIsUsed";
        public const string UserIsNotFound = "UserIsNotFound";
        public const string FailedToRemoveOldRoles = "FailedToRemoveOldRoles";
        public const string FailedToUpdateUserRoles = "FailedToUpdateUserRoles";
        public const string FailedToAddNewRoles = "FailedToAddNewRoles";
        public const string FailedToUpdateClaims = "FailedToUpdateClaims";
        public const string FailedToAddNewClaims = "FailedToAddNewClaims";
        public const string FailedToRemoveOldClaims = "FailedToRemoveOldClaims";
        public const string Email = "Email";
        public const string Message = "Message";
        public const string SendEmailFailed = "SendEmailFailed";
        public const string EmailNotConfirmed = "EmailNotConfirmed";
        public const string TryToRegisterAgain = "TryToRegisterAgain";
        public const string ErrorWhenConfirmEmail = "ErrorWhenConfirmEmail";
        public const string ConfirmEmailDone = "ConfirmEmailDone";
        public const string TryAgainInAnotherTime = "TryAgainInAnotherTime";
        public const string InvaildCode = "InvaildCode";
        public const string NoImage = "NoImage";
        public const string FailedToUploadImage = "FailedToUploadImage";
    }
}
