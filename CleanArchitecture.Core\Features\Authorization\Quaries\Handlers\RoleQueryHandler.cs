﻿using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using Graduation.Core.Bases;
using Graduation.Core.Features.Authorization.Quaries.Models;
using Graduation.Core.Features.Authorization.Quaries.Results;
using Graduation.Core.Resources;
using Graduation.Data.Entities.Identity;
using Graduation.Data.Results;
using Graduation.Service.Abstract;

namespace Graduation.Core.Features.Authorization.Quaries.Handlers
{
    public class RoleQueryHandler : ResponseH<PERSON><PERSON>,
       IRequestHandler<GetRolesListQuery, Response<List<GetRolesResult>>>,
       IRequestHandler<GetRoleByIdQuery, Response<GetRolesResult>>,
       IRequestHandler<GetUserRolesQuery, Response<List<string>>>
    {
        #region Fields
        private readonly IAuthorizationService _authorizationService;
        private readonly IMapper _mapper;
        private readonly UserManager<AppUser> _userManager;
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;
        #endregion
        #region Constructors
        public RoleQueryHandler(
                                IAuthorizationService authorizationService,
                                IMapper mapper,
                                UserManager<AppUser> userManager,
                                IStringLocalizer<SharedResources> sharedLocalizer) : base(sharedLocalizer)
        {
            _authorizationService=authorizationService;
            _mapper=mapper;
            _userManager=userManager;
            _sharedLocalizer = sharedLocalizer;
        }
        #endregion
        #region Handle Functions
        public async Task<Response<List<GetRolesResult>>> Handle(GetRolesListQuery request, CancellationToken cancellationToken)
        {
            var roles = await _authorizationService.GetRolesList();
            var result = _mapper.Map<List<GetRolesResult>>(roles);
            return Success(result);
        }

        public async Task<Response<GetRolesResult>> Handle(GetRoleByIdQuery request, CancellationToken cancellationToken)
        {
            var role = await _authorizationService.GetRoleById(request.Id);
            if (role==null) return NotFound<GetRolesResult>(_sharedLocalizer[SharedResourcesKeys.RoleNotExist]);
            var result = _mapper.Map<GetRolesResult>(role);
            return Success(result);
        }

        public async Task<Response<List<string>>> Handle(GetUserRolesQuery request, CancellationToken cancellationToken)
        {
            var user = await _userManager.FindByIdAsync(request.UserId);
            if (user==null) return NotFound<List<string>>(_sharedLocalizer[SharedResourcesKeys.UserIsNotFound]);
            var result = await _authorizationService.GetUserRoles(user);
            return Success(result);
        }
        #endregion
    }
}
