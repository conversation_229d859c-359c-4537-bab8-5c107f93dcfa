﻿using FluentValidation;
using Microsoft.Extensions.Localization;
using Graduation.Core.Features.Authorization.Commands.Models;
using Graduation.Core.Resources;
using Graduation.Service.Abstract;

namespace Graduation.Core.Features.Authorization.Commands.Validators
{
    public class DeleteRoleValidator : AbstractValidator<DeleteRoleCommand>
    {
        #region Fields
        
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;

        #endregion
        #region Constructors
        public DeleteRoleValidator( 
                                   IStringLocalizer<SharedResources> sharedLocalizer)
        {
      
            _sharedLocalizer = sharedLocalizer;
            ApplyValidationsRules();
            ApplyCustomValidationsRules();
        }
        #endregion
        #region  Functions
        public void ApplyValidationsRules()
        {
            RuleFor(x => x.Id)
                 .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                 .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required]);
        }
        public void ApplyCustomValidationsRules()
        {
            
        }
        #endregion
    }
}
