﻿using MediatR;
using Microsoft.Extensions.Localization;
using Graduation.Core.Bases;
using Graduation.Core.Features.Authentication.Queries.Models;
using Graduation.Core.Resources;
using Graduation.Service.Abstract;

namespace Graduation.Core.Features.Authentication.Queries.Handles
{
    public class AuthenticationQueryHandler : <PERSON><PERSON><PERSON><PERSON>,
        IRequestHandler<AuthorizeUserQuery, Response<string>>,
        IRequestHandler<ConfirmEmailQuery, Response<string>>,
        IRequestHandler<ConfirmResetPasswordQuery, Response<string>>
    {


        #region Fields
        
        private readonly IAuthenticationService _authenticationService;
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;

        #endregion

        #region Constructors
        public AuthenticationQueryHandler(
                                            IAuthenticationService authenticationService,
                                            IStringLocalizer<SharedResources> sharedLocalizer) : base(sharedLocalizer)
        {
            
            _authenticationService=authenticationService;
            _sharedLocalizer = sharedLocalizer;
        }


        #endregion

        #region Handle Functions
        public async Task<Response<string>> Handle(AuthorizeUserQuery request, CancellationToken cancellationToken)
        {
            var result = await _authenticationService.ValidateToken(request.AccessToken);
            if (result=="NotExpired")
                return Success(result);
            return Unauthorized<string>();
        }

        public async Task<Response<string>> Handle(ConfirmEmailQuery request, CancellationToken cancellationToken)
        {
            var confirmEmail = await _authenticationService.ConfirmEmail(request.UserId, request.Code);
            if (confirmEmail=="ErrorWhenConfirmEmail")
                return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.ErrorWhenConfirmEmail]);
            return Success<string>(_sharedLocalizer[SharedResourcesKeys.ConfirmEmailDone]);
        }

        public async Task<Response<string>> Handle(ConfirmResetPasswordQuery request, CancellationToken cancellationToken)
        {
            var result = await _authenticationService.ConfirmResetPassword(request.Code, request.Email);
            switch (result.Status)
            { 
                case "InvalidCode": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.InvaildCode]);
                case "CodeExpired": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.TokenIsExpired]);
                case "CodeNotFound": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.InvaildCode]);
                case "UserNotFound": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.UserIsNotFound]);
                case "Failed": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.InvaildCode]);
                case "Success": return Success<string>(result.ResetToken!);
                default: return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.InvaildCode]);
            }
        }
        #endregion
    }
}

