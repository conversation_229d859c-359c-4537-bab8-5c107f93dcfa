﻿using System.Reflection;

namespace Graduation.Data.Utilities
{
    public static class Roles
    {
        public const string Admin = "Admin";
        public const string User = "User";
        public const string Organizer = "Organizer";

        public static IEnumerable<string> GetAll()
        {
            return typeof(Roles)
                .GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)
                .Where(f => f.IsLiteral && !f.IsInitOnly) // pick const fields only
                .Select(f => f.GetRawConstantValue()?.ToString()!)
                .ToList();
        }
    }
}
