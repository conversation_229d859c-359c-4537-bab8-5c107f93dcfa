﻿
using Graduation.Data.Entities.Identity;
using Graduation.Infrustructure.Context;
using Graduation.Infrustructure.Context.DataSeeding;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Graduation.Core.MiddleWares;

public static class DbPreProcessMiddleware
{
    public static async void DbPreProcess(this WebApplication app )
    {
        using var scope = app.Services.CreateScope();
        var services = scope.ServiceProvider;
        var dbContext = services.GetRequiredService<ApplicationDbContext>();
        
        var userManager = services.GetRequiredService<UserManager<AppUser>>();
        var loggerFactory = services.GetRequiredService<ILoggerFactory>();
        try
        {
            await dbContext.Database.MigrateAsync();
          //  Seeding.SeedingHelper(dbContext);

            await IdentitySeeder.SeedRolesAndAdmin(userManager, services.GetRequiredService<RoleManager<IdentityRole>>());
        }
        catch (Exception ex)
        {
            var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();

            logger.LogError(ex.Message);

        }

    }

}
