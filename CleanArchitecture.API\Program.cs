
using Data.Helper;
using Graduation.API.Extentions;
using Graduation.Core;
using Graduation.Core.Bases;
using Graduation.Core.MiddleWares;
using Graduation.Data.Helper;
using Graduation.Infrustructure;
using Graduation.Infrustructure.Context;
using Graduation.Service;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Serilog;
using System;
using System.Globalization;


namespace Graduation.API
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            builder.Services.AddApplicationDependencies( builder.Configuration);

            var app = builder.Build();

            app.DbPreProcess();

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
            
            }
            app.UseSwagger();
            app.UseSwaggerUI();
            app.MapOpenApi();
            #region Localization Middleware
            var options = app.Services.GetService<IOptions<RequestLocalizationOptions>>();
            app.UseRequestLocalization(options.Value);
            #endregion

            app.UseMiddleware<ErrorHandlerMiddleware>();
            app.UseCors(op =>
            {
                op.AllowAnyOrigin()
                  .AllowAnyMethod()
                  .AllowAnyHeader();
            });
            app.UseHttpsRedirection();

    
            app.UseAuthentication();

            app.UseAuthorization();

            app.MapControllers();

            app.Run();
        }
    }
}
