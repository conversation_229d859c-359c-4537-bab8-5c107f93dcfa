﻿using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using Graduation.Core.Bases;
using Graduation.Core.Features.ApplicationUser.Queries.Models;
using Graduation.Core.Features.ApplicationUser.Queries.Results;
using Graduation.Core.Resources;
using Graduation.Core.Warppars;
using Graduation.Data.Entities.Identity;

namespace Graduation.Core.Features.ApplicationUser.Queries.Handlers
{
    public class UserQueryHandler : Response<PERSON><PERSON><PERSON>,
         IRequestHandler<GetUserPaginationQuery, PaginatedResult<GetUserPaginationReponse>>,
         IRequestHandler<GetUserByIdQuery, Response<GetUserByIdResponse>>,
        IRequestHandler<GetUserIdByEmailQuery, Response<string>>,
        IRequestHandler<GetUserIdByUserNameQuery, Response<string>>
    {
        #region Fields
        private readonly IMapper _mapper;
        private readonly UserManager<AppUser> _userManager;
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;
        #endregion

        #region Constructors
        public UserQueryHandler(
                                  IMapper mapper,
                                  UserManager<AppUser> userManager,
                                  IStringLocalizer<SharedResources> sharedLocalizer) : base(sharedLocalizer)
        {
            _mapper = mapper;
            _userManager = userManager;
            _sharedLocalizer = sharedLocalizer;
        }
        #endregion

        #region Handle Functions
        public async Task<PaginatedResult<GetUserPaginationReponse>> Handle(GetUserPaginationQuery request, CancellationToken cancellationToken)
        {
            var users = _userManager.Users.Skip((request.PageNumber - 1) * request.PageSize).Take(request.PageSize).ToList();
            var count = _userManager.Users.Count();
            var usersToReturn = _mapper.Map<List<GetUserPaginationReponse>>(users);
            var paginatedList = new PaginatedResult<GetUserPaginationReponse>(page: request.PageNumber,
                                                                           pageSize: request.PageSize,
                                                                           count: count,
                                                                           data: usersToReturn
                                                                           );

            return paginatedList;
        }

        public async Task<Response<GetUserByIdResponse>> Handle(GetUserByIdQuery request, CancellationToken cancellationToken)
        {
            //var user = await _userManager.Users.FirstOrDefaultAsync(x => x.Id==request.Id);
            var user = await _userManager.FindByIdAsync(request.Id.ToString());
            if (user == null) return NotFound<GetUserByIdResponse>(_sharedLocalizer[SharedResourcesKeys.NotFound]);
            var result = _mapper.Map<GetUserByIdResponse>(user);
            return Success(result);
        }

        public async Task<Response<string>> Handle(GetUserIdByEmailQuery request, CancellationToken cancellationToken)
        {
            var user = await _userManager.FindByEmailAsync(request.Email);
            if (user == null) return NotFound<string>(_sharedLocalizer[SharedResourcesKeys.NotFound]);
            return Success(user.Id);

        }

        public async Task<Response<string>> Handle(GetUserIdByUserNameQuery request, CancellationToken cancellationToken)
        {
            
            var user = await _userManager.FindByNameAsync(request.UserName);
            if (user == null) return NotFound<string>(_sharedLocalizer[SharedResourcesKeys.NotFound]);
            return Success(user.Id);

        }
        #endregion
    }
}
