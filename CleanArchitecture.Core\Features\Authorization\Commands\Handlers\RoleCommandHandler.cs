﻿using MediatR;
using Microsoft.Extensions.Localization;
using Graduation.Core.Bases;
using Graduation.Core.Features.Authorization.Commands.Models;
using Graduation.Core.Resources;
using Graduation.Service.Abstract;
namespace Graduation.Core.Features.Authorization.Commands.Handlers
{
    public class RoleCommandHandler : <PERSON><PERSON><PERSON><PERSON>,
        IRequestHandler<AddRoleCommand, Response<string>>,
        IRequestHandler<EditRoleCommand, Response<string>>,
        IRequestHandler<DeleteRoleCommand, Response<string>>,
        IRequestHandler<UpdateUserRolesCommand, Response<string>>
    {
        #region MyRegion
        
        private readonly IAuthorizationService _authorizationService;
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;

        #endregion
        #region MyRegion
        public RoleCommandHandler(
                                  IAuthorizationService authorizationService,
                                  IStringLocalizer<SharedResources> sharedLocalizer) : base(sharedLocalizer)
        {
           
            _authorizationService = authorizationService;
            _sharedLocalizer = sharedLocalizer;
        }

        #endregion
        #region MyRegion
        public async Task<Response<string>> Handle(AddRoleCommand request, CancellationToken cancellationToken)
        {
            var result = await _authorizationService.AddRoleAsync(request.RoleName);
            if (result=="Success") return Success("");
            return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.AddFailed]);
        }

        public async Task<Response<string>> Handle(EditRoleCommand request, CancellationToken cancellationToken)
        {
            var result = await _authorizationService.EditRoleAsync(request);
            if (result == "notFound") return NotFound<string>();
            else if (result == "Success") return Success<string>(_sharedLocalizer[SharedResourcesKeys.Updated]);
            else
                return BadRequest<string>(result);
        }

        public async Task<Response<string>> Handle(DeleteRoleCommand request, CancellationToken cancellationToken)
        {
            var result = await _authorizationService.DeleteRoleAsync(request.Id);
            if (result == "NotFound") return NotFound<string>();
            else if (result == "Used") return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.RoleIsUsed]);
            else if (result == "Success") return Success<string>(_sharedLocalizer[SharedResourcesKeys.Deleted]);
            else
                return BadRequest<string>(result);
        }
        public async Task<Response<string>> Handle(UpdateUserRolesCommand request, CancellationToken cancellationToken)
        {
            var result = await _authorizationService.UpdateUserRoles(request.UserId,request.Roles);
            switch (result)
            {
                case "UserIsNull": return NotFound<string>(_sharedLocalizer[SharedResourcesKeys.UserIsNotFound]);
                case "FailedToRemoveOldRoles": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.FailedToRemoveOldRoles]);
                case "FailedToAddNewRoles": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.FailedToAddNewRoles]);
                case "FailedToUpdateUserRoles": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.FailedToUpdateUserRoles]);
            }
            return Success<string>(_sharedLocalizer[SharedResourcesKeys.Success]);
        }
        #endregion

    }
}
