﻿using FluentValidation;
using Microsoft.Extensions.Localization;
using Graduation.Core.Features.Authorization.Commands.Models;
using Graduation.Core.Resources;
using Graduation.Service.Abstract;

namespace Graduation.Core.Features.Authorization.Commands.Validators
{
    public class EditRoleValidator : AbstractValidator<EditRoleCommand>
    {
        #region Fields
        
        private readonly IAuthorizationService _authorizationService;
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;
        #endregion
        #region Constructors

        #endregion
        public EditRoleValidator( IAuthorizationService authorizationService,
                                 IStringLocalizer<SharedResources> sharedLocalizer)
        {
           
            this._authorizationService = authorizationService;
            _sharedLocalizer = sharedLocalizer;
            ApplyValidationsRules();
            ApplyCustomValidationsRules();
        }

        #region Actions
        public void ApplyValidationsRules()
        {
            RuleFor(x => x.Id)
                 .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                 .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required]);

            RuleFor(x => x.Name)
                 .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                 .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required]);
        }

        public void ApplyCustomValidationsRules()
        {

            RuleFor(x => x.Name)
                .MustAsync(async (Key, CancellationToken) => !await _authorizationService.IsRoleExistByName(Key))
                .WithMessage(_sharedLocalizer[SharedResourcesKeys.IsExist]);

        }

        #endregion
    }
}
