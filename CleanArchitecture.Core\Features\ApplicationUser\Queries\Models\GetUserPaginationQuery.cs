﻿using MediatR;
using Graduation.Core.Features.ApplicationUser.Queries.Results;

using Graduation.Core.Warppars;
using Graduation.Core.Bases.Contracts;

namespace Graduation.Core.Features.ApplicationUser.Queries.Models
{
    public class GetUserPaginationQuery : IRequest<PaginatedResult<GetUserPaginationReponse>>,ICacheableRequest
    {
        public int PageNumber { get; set; }
        public int PageSize { get; set; }

        public TimeSpan? Expiration => TimeSpan.FromMinutes(30);
    }
}
