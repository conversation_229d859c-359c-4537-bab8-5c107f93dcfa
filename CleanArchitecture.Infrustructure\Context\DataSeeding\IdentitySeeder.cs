﻿
using Graduation.Data.Entities.Identity;
using Graduation.Data.Utilities;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Graduation.Infrustructure.Context.DataSeeding
{
    public class IdentitySeeder
    {
        public static async Task SeedRolesAndAdmin(UserManager<AppUser> userManager, RoleManager<IdentityRole> roleManager)
        {
            try
            {
                var existingAdmin = await userManager.FindByEmailAsync("<EMAIL>");
                if (existingAdmin != null)
                {
                    return; 
                }

                var roles = Roles.GetAll();
                foreach (var role in roles)
                {
                    if (!await roleManager.RoleExistsAsync(role))
                    {
                        await roleManager.CreateAsync(new IdentityRole(role));
                    }
                }

                var newAdmin = new AppUser { UserName = "admin", Email = "<EMAIL>", FullName = "System Admin" };
                var result = await userManager.CreateAsync(newAdmin, "Admin@123");

                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(newAdmin, "Admin");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in IdentitySeeder: {ex.Message}");
            }
        }

    }
}
