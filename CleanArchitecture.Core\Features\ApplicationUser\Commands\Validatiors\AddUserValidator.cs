﻿using FluentValidation;
using Microsoft.Extensions.Localization;
using Graduation.Core.Features.ApplicationUser.Commands.Models;
using Graduation.Core.Resources;

namespace Graduation.Core.Features.ApplicationUser.Commands.Validatiors
{
    public class AddUserValidator : AbstractValidator<AddUserCommand>
    {
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;

        #region Constructors
        public AddUserValidator(IStringLocalizer<SharedResources> sharedLocalizer)
        {
            _sharedLocalizer = sharedLocalizer;
            ApplyValidationsRules();
            ApplyCustomValidationsRules();
        }
        #endregion

        #region Handle Functions
        public void ApplyValidationsRules()
        {
            RuleFor(x => x.FullName)
                 .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                 .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required])
                 .MaximumLength(100).WithMessage(_sharedLocalizer[SharedResourcesKeys.MaxLengthis100]);

            RuleFor(x => x.UserName)
                .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required])
                .MaximumLength(100).WithMessage(_sharedLocalizer[SharedResourcesKeys.MaxLengthis100]);

            RuleFor(x => x.Email)
                 .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                 .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required]);
            RuleFor(x => x.Password)
                 .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                 .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required]);
            RuleFor(x => x.ConfirmPassword)
                 .Equal(x => x.Password).WithMessage(_sharedLocalizer[SharedResourcesKeys.PasswordNotEqualConfirmPass]);

        }

        public void ApplyCustomValidationsRules()
        {

        }

        #endregion
    }
}
