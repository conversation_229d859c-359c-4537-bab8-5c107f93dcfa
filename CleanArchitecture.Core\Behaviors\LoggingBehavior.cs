﻿using MediatR;
using Serilog;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace Graduation.Core.Behaviors
{
    
    public class LoggingBehavior<TRequest, TResponse>
        : IPipelineBehavior<TRequest, TResponse>
    {
        public async Task<TResponse> Handle(
            TRequest request,
            RequestHandlerDelegate<TResponse> next,
            CancellationToken cancellationToken)
        {
            var requestName = typeof(TRequest).Name;
            Log.Information("Handling request {RequestName} with payload {@Request}", requestName, request);

            var stopwatch = Stopwatch.StartNew();

            try
            {
                var response = await next();

                stopwatch.Stop();
                Log.Information("Handled request {RequestName} in {ElapsedMs}ms with response {@Response}",
                    requestName, stopwatch.ElapsedMilliseconds, response);

                return response;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Log.Error(ex, "Error handling request {RequestName} after {ElapsedMs}ms with payload {@Request}",
                    requestName, stopwatch.ElapsedMilliseconds, request);

                throw; // rethrow so it can be handled by global exception handling
            }
        }
    }

}
