﻿using Graduation.Data.Entities.Identity;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Graduation.Service.Abstract
{
    public interface IApplicationUserService
    {
        Task<List<string>?> AddUserAsync(AppUser user, string password);
        Task<bool> IsUserExist(string userId);
        Task<List<string>?> EditUserAsync(AppUser user);
        Task<List<string>?> DeleteUserAsync(string userId);
        Task<List<string>?> ChangePasswordAsync(string userId, string currentPassword, string newPassword);
    }
}
