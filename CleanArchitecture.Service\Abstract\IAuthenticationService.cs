﻿using Graduation.Data.Entities.Identity;
using Graduation.Data.Results;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Graduation.Service.Abstract
{
    public interface IAuthenticationService
    {
        public Task<JwtAuthResult> GetJWTToken(AppUser user);
        public JwtSecurityToken ReadJWTToken(string accessToken);
        public Task<(string, DateTime?)> ValidateDetails(JwtSecurityToken jwtToken, string AccessToken, string RefreshToken);
        public Task<JwtAuthResult> GetRefreshToken(AppUser user, JwtSecurityToken jwtToken, DateTime? expiryDate, string refreshToken);
        public Task<string> ValidateToken(string AccessToken);
        public Task<string> ConfirmEmail(string? userId, string? code);
        public Task<string> SendResetPasswordCode(string Email);
        public Task<(string Status, string? ResetToken)> ConfirmResetPassword(string email, string code);
        public Task<string> ResetPassword(string email, string resetToken, string newPassword);

    }
}
