﻿using Graduation.API.Controllers;
using Graduation.Core.Bases;
//using Graduation.Api.Base;
using Graduation.Core.Features.ApplicationUser.Commands.Models;
using Graduation.Core.Features.ApplicationUser.Queries.Models;
using Graduation.Core.Filters;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
//using Data.AppMetaData;
namespace Graduation.Api.Controllers
{
   // [Authorize(Roles = "Admin,User")]
    public class ApplicationUserController : AppControllerBase
    {
        [HttpPost("SignUp")]
        public async Task<IActionResult> Create([FromBody] AddUserCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }
        [Authorize(Roles = "Admin")]
        [HttpGet("GetAllUsers")]
        public async Task<IActionResult> Paginated([FromQuery] GetUserPaginationQuery query)
        {
            var response = await Mediator.Send(query);
            return Ok(response);
        }
        [Authorize(Roles = "Admin")]
        [HttpGet("GetUserById/{id}")]
        public async Task<IActionResult> GetStudentByID([FromRoute] string id)
        {
            return NewResult(await Mediator.Send(new GetUserByIdQuery(id)));
        }
        [Authorize(Roles = "Admin")]

        [HttpPut("EditUser")]
        public async Task<IActionResult> Edit([FromBody] EditUserCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [Authorize(Roles = "Admin")]
        [HttpDelete("DeleteUser/{id}")]
        
        public async Task<IActionResult> Delete([FromRoute] string id)
        {
            return NewResult(await Mediator.Send(new DeleteUserCommand(id)));
        }
        [Authorize(Roles = "Admin")]
        [HttpPut("ChangePassword")]
        public async Task<IActionResult> ChangePassword([FromBody] ChangeUserPasswordCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }
        [Authorize(Roles = "Admin")]

        [HttpGet("UserIdByEmail/{email}")]
        public async Task<IActionResult> GetUserIdByEmail([FromRoute] string email)
        {
            return NewResult(await Mediator.Send(new GetUserIdByEmailQuery(email)));
        }
        [Authorize(Roles = "Admin")]

        [HttpGet("UserIdByUserName/{userName}")]
        public async Task<IActionResult> GetUserIdByUserName([FromRoute] string userName)
        {
            return NewResult(await Mediator.Send(new GetUserIdByUserNameQuery(userName)));
        }
        [Authorize]
        [HttpGet("CurrentUserId")]
        public IActionResult GetCurrentUserId()
        {
            var userId = User.Claims.FirstOrDefault(c => c.Type == "Id")?.Value;
            return Ok( new Response<string>("Added Successfully") { StatusCode= System.Net.HttpStatusCode.OK ,Succeeded = true , Data = userId  } );
        }
    }
}
