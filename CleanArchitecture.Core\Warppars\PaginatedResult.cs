﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Graduation.Core.Warppars
{
    public class PaginatedResult<T>
    {
        public PaginatedResult(List<T> data)
        {
            Data = data;
        }
      
        internal PaginatedResult( List<T> data = default, List<string> messages = null, int count = 0, int page = 1, int pageSize = 10)
        {
            Meta = new Meta();
            StatusCode = HttpStatusCode.OK;
            Data = data;
            Meta.CurrentPage = page;
            Meta.PageSize = pageSize;
            Meta.TotalPages = (int)Math.Ceiling(count / (double)pageSize);
            Meta.TotalCount = count;
        }
        public HttpStatusCode StatusCode { get; set; }

        public Meta Meta { get; set; }
        public bool Succeeded { get; set; } = true;
        public List<string> Messages { get; set; } = new();
        public List<string> Errors { get; set; } = null;

        public List<T> Data { get; set; }

       

    }
    public class Meta
    {
        public int CurrentPage { get; set; }

        public int TotalPages { get; set; }

        public int TotalCount { get; set; }


        public int PageSize { get; set; }

        public bool HasPreviousPage => CurrentPage > 1;

        public bool HasNextPage => CurrentPage < TotalPages;



    }

}
