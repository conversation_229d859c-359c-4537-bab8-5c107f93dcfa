﻿using MediatR;
using Microsoft.Extensions.Localization;
using Graduation.Core.Bases;
using Graduation.Core.Features.Authorization.Commands.Models;
using Graduation.Core.Resources;
using Graduation.Service.Abstract;

namespace Graduation.Core.Features.Authorization.Commands.Handlers
{
    public class ClaimsCommandHandler : ResponseH<PERSON>ler,
         IRequestHandler<UpdateUserClaimsCommand, Response<string>>
    {
        #region Fileds
        
        private readonly IAuthorizationService _authorizationService;
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;

        #endregion
        #region Constructors
        public ClaimsCommandHandler(
                                    IAuthorizationService authorizationService,
                                    IStringLocalizer<SharedResources> sharedLocalizer) : base(sharedLocalizer)
        {
            _authorizationService= authorizationService;
            _sharedLocalizer = sharedLocalizer;
        }
        #endregion
        #region Handle Functions
        public async Task<Response<string>> Handle(UpdateUserClaimsCommand request, CancellationToken cancellationToken)
        {
            var result = await _authorizationService.UpdateUserClaims(request.UserId,request.userClaims);
            switch (result)
            {
                case "UserIsNull": return NotFound<string>(_sharedLocalizer[SharedResourcesKeys.UserIsNotFound]);
                case "FailedToRemoveOldClaims": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.FailedToRemoveOldClaims]);
                case "FailedToAddNewClaims": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.FailedToAddNewClaims]);
                case "FailedToUpdateClaims": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.FailedToUpdateClaims]);
            }
            return Success<string>(_sharedLocalizer[SharedResourcesKeys.Success]);
        }
        #endregion
    }
}
