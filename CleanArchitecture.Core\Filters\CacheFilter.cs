﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Graduation.Core.Filters
{

    public class CacheFilter : IAsyncActionFilter
    {
        private readonly int timeToCache;
        private readonly IMemoryCache _cacheService;

        public CacheFilter(int TimeToCache, IMemoryCache memoryCache)
        {

            timeToCache = TimeToCache;
            this._cacheService = memoryCache;
        }
        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            // var _cacheService = context.HttpContext.RequestServices.GetService() ;
            string key = GenerateCacheKey(context.HttpContext.Request);


            if (_cacheService.TryGetValue(key, out var result))
            {
                context.Result = new ObjectResult(result)
                {
                    StatusCode = 200
                };
                return;
            }
            var executedContext = await next.Invoke();
            if (executedContext.Result is ObjectResult objectResult && objectResult.Value is not null)
            {
                var cacheData = objectResult.Value;

                _cacheService.Set(key, cacheData, TimeSpan.FromMinutes(timeToCache));

            }

        }

        private string GenerateCacheKey(HttpRequest request)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(request.Path);
            foreach (var item in request.Query.OrderBy(x => x.Key))
            {
                sb.Append($"|{item.Key}={item.Value}");
            }
            return sb.ToString();
        }
    }
}
