﻿using Graduation.Core.Bases;
using Graduation.Core.Features.Authentication.Commands.Models;
using Graduation.Core.Resources;
using Graduation.Data.Entities.Identity;
using Graduation.Data.Results;
using Graduation.Service.Abstract;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Graduation.Core.Features.Authentication.Commands.Handlers
{
    public class AuthenticationCommandHandelr : ResponseHandler, 
        IRequestHandler<SignInCommand, Response<JwtAuthResult>>,
        IRequestHandler<RefreshTokenCommand, Response<JwtAuthResult>>,
        IRequestHandler<SendResetPasswordCommand, Response<string>>,
        IRequestHandler<ResetPasswordCommand, Response<string>>

    {
        private readonly UserManager<AppUser> _userManager;
        private readonly SignInManager<AppUser> _signInManager;
        private readonly IAuthenticationService _authenticationService;
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;
        

        public AuthenticationCommandHandelr(UserManager<AppUser> userManager,
            SignInManager<AppUser> signInManager, 
            IAuthenticationService authenticationService,
            IStringLocalizer<SharedResources> sharedLocalizer
             
            ) : base(sharedLocalizer)
        {
            this._userManager = userManager;
            this._signInManager = signInManager;
            this._authenticationService = authenticationService;
            this._sharedLocalizer = sharedLocalizer;
        }
        public async Task<Response<JwtAuthResult>> Handle(SignInCommand request, CancellationToken cancellationToken)
        {
            //Check if user is exist or not
            var user = await _userManager.FindByEmailAsync(request.Email);
            //Return The UserName Not Found
            if (user == null) return BadRequest<JwtAuthResult>(_sharedLocalizer[SharedResourcesKeys.UserNameIsNotExist]);
            //try To Sign in 
            var signInResult = await _signInManager.CheckPasswordSignInAsync(user, request.Password, false);
            //if Failed Return Passord is wrong
            if (!signInResult.Succeeded) return BadRequest<JwtAuthResult>(_sharedLocalizer[SharedResourcesKeys.PasswordNotCorrect]);
            //confirm email
            //if (!user.EmailConfirmed)
            //    return BadRequest<JwtAuthResult>(SharedResourcesKeys.EmailNotConfirmed]);
            //Generate Token
            var result = await _authenticationService.GetJWTToken(user);
            //return Token 
            return Success(result);
        }

        public async Task<Response<JwtAuthResult>> Handle(RefreshTokenCommand request, CancellationToken cancellationToken)
        {
            var jwtToken = _authenticationService.ReadJWTToken(request.AccessToken);
            var userIdAndExpireDate = await _authenticationService.ValidateDetails(jwtToken, request.AccessToken, request.RefreshToken);
            switch (userIdAndExpireDate)
            {
                case ("AlgorithmIsWrong", null): return Unauthorized<JwtAuthResult>(_sharedLocalizer[SharedResourcesKeys.AlgorithmIsWrong]);
                case ("TokenIsNotExpired", null): return Unauthorized<JwtAuthResult>(_sharedLocalizer[SharedResourcesKeys.TokenIsNotExpired]);
                case ("RefreshTokenIsNotFound", null): return Unauthorized<JwtAuthResult>(_sharedLocalizer[SharedResourcesKeys.RefreshTokenIsNotFound]);
                case ("RefreshTokenIsExpired", null): return Unauthorized<JwtAuthResult>(_sharedLocalizer[SharedResourcesKeys.RefreshTokenIsExpired]);
            }
            var (userId, expiryDate) = userIdAndExpireDate;
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return NotFound<JwtAuthResult>();
            }
            var result = await _authenticationService.GetRefreshToken(user, jwtToken, expiryDate, request.RefreshToken);
            return Success(result);

        }

        public async Task<Response<string>> Handle(ResetPasswordCommand request, CancellationToken cancellationToken)
        {

            var result = await _authenticationService.ResetPassword(request.Email, request.Password , request.ResetToken);
            switch (result)
            {
                case "SessionExpired": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.TokenIsExpired]);
                case "InvalidSession": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.InvaildCode]);
                case "UserNotFound": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.UserIsNotFound]);
                case "Failed": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.ChangePassFailed]);
                case "Success": return Success<string>(_sharedLocalizer[SharedResourcesKeys.Success]);
                default: return BadRequest<string>(result);
            }
        }

        public async Task<Response<string>> Handle(SendResetPasswordCommand request, CancellationToken cancellationToken)
        {
            var result = await _authenticationService.SendResetPasswordCode(request.Email);
            switch (result)
            {
                case "UserNotFound": return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.UserIsNotFound]);
                case "Success": return Success<string>(_sharedLocalizer[SharedResourcesKeys.Success]);
                default: return BadRequest<string>(_sharedLocalizer[SharedResourcesKeys.SendEmailFailed]);
            }
        }
    }
}
