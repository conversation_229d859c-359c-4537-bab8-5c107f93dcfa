﻿using FluentValidation;
using Microsoft.Extensions.Localization;
using Graduation.Core.Features.Authorization.Commands.Models;
using Graduation.Core.Resources;
using Graduation.Service.Abstract;

namespace Graduation.Core.Features.Authorization.Commands.Validators
{
    public class AddRoleValidators : AbstractValidator<AddRoleCommand>
    {
        #region Fields
        
        private readonly IAuthorizationService _authorizationService;
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;
        #endregion
        public AddRoleValidators(
                                 IAuthorizationService authorizationService,
                                 IStringLocalizer<SharedResources> sharedLocalizer)
        {
           
            _authorizationService = authorizationService;
            _sharedLocalizer = sharedLocalizer;
            ApplyValidationsRules();
            ApplyCustomValidationsRules();
        }

        #region Actions
        public void ApplyValidationsRules()
        {
            RuleFor(x => x.RoleName)
                 .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                 .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required]);
        }

        public void ApplyCustomValidationsRules()
        {
            RuleFor(x => x.RoleName)
                .MustAsync(async (Key, CancellationToken) => !await _authorizationService.IsRoleExistByName(Key))
                .WithMessage(_sharedLocalizer[SharedResourcesKeys.IsExist]);
        }

        #endregion
    }
}
