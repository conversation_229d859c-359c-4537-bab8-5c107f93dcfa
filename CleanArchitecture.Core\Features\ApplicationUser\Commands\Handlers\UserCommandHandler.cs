﻿using AutoMapper;
using Graduation.Core.Bases;
using Graduation.Core.Features.ApplicationUser.Commands.Models;
using Graduation.Core.Resources;
using Graduation.Data.Entities.Identity;
using Graduation.Service.Abstract;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;

namespace Graduation.Core.Features.ApplicationUser.Commands.Handlers
{
    public class UserCommandHandler : ResponseHandler,
        IRequestHandler<AddUserCommand, Response<string>>,
        IRequestHandler<EditUserCommand, Response<string>>,
        IRequestHandler<DeleteUserCommand, Response<string>>,
        IRequestHandler<ChangeUserPasswordCommand, Response<string>>
    {
        #region Fields
        private readonly IMapper _mapper;
        private readonly IApplicationUserService _applicationUserService;
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;
        #endregion

        #region Constructors
        public UserCommandHandler(
                                  IMapper mapper,
                                  IApplicationUserService applicationUserService,
                                  IStringLocalizer<SharedResources> sharedLocalizer
            ) : base(sharedLocalizer)
        {
            _mapper = mapper;
            _applicationUserService = applicationUserService;
            _sharedLocalizer = sharedLocalizer;
        }
        #endregion

        #region Handle Functions
        public async Task<Response<string>> Handle(AddUserCommand request, CancellationToken cancellationToken)
        {
            var identityUser = _mapper.Map<AppUser>(request);
            var createResult = await _applicationUserService.AddUserAsync(identityUser, request.Password);
  
            if (createResult == null) 
                return Success<string>("");

            Log.Error($"Error When Registering The User:{request.UserName}");
            return BadRequest<string>(createResult, _sharedLocalizer[SharedResourcesKeys.TryToRegisterAgain]);
        }

        public async Task<Response<string>> Handle(EditUserCommand request, CancellationToken cancellationToken)
        {
            var newUser = _mapper.Map<AppUser>(request);
            var result = await _applicationUserService.EditUserAsync(newUser);
            
            switch (result[0])
            { 
                case "UserNameIsExist":
                    return NotFound<string>(_sharedLocalizer[SharedResourcesKeys.UserIsNotFound]);
                case "Success":
                    return Success<string>(_sharedLocalizer[SharedResourcesKeys.Success]);
                default:
                    Log.Error($"Error When Editing The User:{request.UserName}");
                    return BadRequest<string>(result, _sharedLocalizer[SharedResourcesKeys.UpdateFailed]);
            }
        }

        public async Task<Response<string>> Handle(DeleteUserCommand request, CancellationToken cancellationToken)
        {
            var result = await _applicationUserService.DeleteUserAsync(request.Id);
            
            if (result == null)
                return Deleted<string>();
                
            if (result.Contains("UserNotFound"))
                return NotFound<string>(_sharedLocalizer[SharedResourcesKeys.UserIsNotFound]);
                
            return BadRequest<string>(result, _sharedLocalizer[SharedResourcesKeys.DeletedFailed]);
        }

        public async Task<Response<string>> Handle(ChangeUserPasswordCommand request, CancellationToken cancellationToken)
        {
            var result = await _applicationUserService.ChangePasswordAsync(request.Id, request.CurrentPassword, request.NewPassword);
            
            if (result == null)
                return Success<string>(_sharedLocalizer[SharedResourcesKeys.Success]);
                
            if (result.Contains("UserNotFound"))
                return NotFound<string>(_sharedLocalizer[SharedResourcesKeys.UserIsNotFound]);
                
            return BadRequest<string>(result, _sharedLocalizer[SharedResourcesKeys.ChangePassFailed]);
        }
        #endregion
    }
}
