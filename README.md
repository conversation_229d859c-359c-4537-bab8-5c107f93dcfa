# Clean Architecture Project

A comprehensive .NET 9.0 Clean Architecture solution implementing authentication, authorization, and user management features with modern architectural patterns and best practices.

## 🏗️ Architecture Overview

This project follows Clean Architecture principles with clear separation of concerns across multiple layers:

```
CleanArchitecture/
├── CleanArchitecture.API/          # Presentation Layer (Web API)
├── CleanArchitecture.Core/         # Application Layer (Business Logic)
├── CleanArchitecture.Data/         # Domain Layer (Entities & Models)
├── CleanArchitecture.Infrastructure/ # Infrastructure Layer (Data Access)
└── CleanArchitecture.Service/      # Service Layer (Application Services)
```

## 🚀 Features

### Authentication & Authorization
- **JWT-based Authentication** with refresh token support
- **Role-based Authorization** (Admin, User, Organizer)
- **Email Confirmation** for user registration
- **Password Reset** functionality via email
- **Token Validation** and refresh mechanisms

### User Management
- **User Registration** and profile management
- **User CRUD Operations** with pagination
- **Role Management** (Add, Edit, Delete roles)
- **Claims-based Authorization**

### Technical Features
- **C<PERSON><PERSON>** with MediatR
- **FluentValidation** for input validation
- **AutoMapper** for object mapping
- **Caching** with pipeline behaviors
- **Logging** with Serilog (Console + SQL Server)
- **Localization** support (English & Arabic)
- **Swagger/OpenAPI** documentation
- **Docker** support

## 🛠️ Technology Stack

### Core Technologies
- **.NET 9.0** - Latest .NET framework
- **ASP.NET Core Web API** - RESTful API framework
- **Entity Framework Core 9.0** - ORM for data access
- **SQL Server** - Database

### Key Packages
- **MediatR 13.0.0** - CQRS implementation
- **AutoMapper 15.0.1** - Object mapping
- **FluentValidation 12.0.0** - Input validation
- **Serilog 4.3.0** - Structured logging
- **Swashbuckle.AspNetCore 9.0.3** - API documentation
- **MailKit 4.13.0** - Email functionality

## 📁 Project Structure

### CleanArchitecture.API (Presentation Layer)
- **Controllers**: API endpoints for authentication, authorization, and user management
- **Extensions**: Dependency injection configuration
- **Program.cs**: Application startup and middleware configuration

### CleanArchitecture.Core (Application Layer)
- **Features**: CQRS commands and queries organized by feature
  - Authentication (SignIn, RefreshToken, Password Reset)
  - Authorization (Role Management, Claims)
  - ApplicationUser (User CRUD operations)
- **Behaviors**: Pipeline behaviors for caching, validation, and logging
- **Bases**: Common response models and base classes
- **Mapping**: AutoMapper profiles
- **Middlewares**: Error handling and custom middleware

### CleanArchitecture.Data (Domain Layer)
- **Entities**: Domain models including AppUser and UserRefreshToken
- **Settings**: Configuration classes for JWT and Email settings
- **Utilities**: Constants and helper classes
- **Results**: DTOs for API responses

### CleanArchitecture.Infrastructure (Infrastructure Layer)
- **Context**: Entity Framework DbContext
- **Repositories**: Generic repository pattern implementation
- **UnitOfWork**: Transaction management
- **Specifications**: Query specification pattern

### CleanArchitecture.Service (Service Layer)
- **Services**: Application service implementations
- **Abstract**: Service interfaces

## 🔧 Getting Started

### Prerequisites
- .NET 9.0 SDK
- SQL Server (Local or Azure)
- Visual Studio 2022 or VS Code

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd CleanArchitecture
   ```

2. **Configure Database**
   - Update connection string in `appsettings.json`
   - Run Entity Framework migrations:
   ```bash
   dotnet ef database update --project CleanArchitecture.Infrastructure --startup-project CleanArchitecture.API
   ```

3. **Configure Email Settings**
   - Update `MailSettings` in `appsettings.json` with your SMTP credentials

4. **Run the Application**
   ```bash
   dotnet run --project CleanArchitecture.API
   ```

5. **Access Swagger Documentation**
   - Navigate to `https://localhost:7000/swagger` (or your configured port)

## 🔐 API Endpoints

### Authentication
- `POST /Authentication/SignIn` - User login
- `POST /Authentication/RefreshToken` - Refresh JWT token
- `GET /Authentication/ValidateToken` - Validate token
- `GET /Authentication/ConfirmEmail` - Confirm email address
- `POST /Authentication/SendResetPasswordCode` - Send password reset code
- `GET /Authentication/ConfirmResetPasswordCode` - Confirm reset code
- `POST /Authentication/ResetPassword` - Reset password

### Authorization
- `GET /Authorization/GetUserRoles` - Get user roles
- `POST /Authorization/UpdateUserRoles` - Update user roles
- `GET /Authorization/GetRoles` - Get all roles
- `POST /Authorization/AddRole` - Add new role
- `PUT /Authorization/EditRole` - Edit existing role
- `DELETE /Authorization/DeleteRole` - Delete role

### User Management
- `GET /ApplicationUser/GetUsers` - Get paginated users
- `POST /ApplicationUser/AddUser` - Add new user
- `PUT /ApplicationUser/EditUser` - Edit user
- `DELETE /ApplicationUser/DeleteUser` - Delete user
- `POST /ApplicationUser/ChangePassword` - Change user password

## 🔧 Configuration

### JWT Settings
```json
{
  "jwtSettings": {
    "Secret": "YourSecretKey",
    "Issuer": "CleanArchitecture",
    "Audience": "WebSite",
    "AccessTokenExpireDate": 1,
    "RefreshTokenExpireDate": 20
  }
}
```

### Email Settings
```json
{
  "MailSettings": {
    "EmailFrom": "<EMAIL>",
    "SmtpHost": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUser": "<EMAIL>",
    "SmtpPass": "your-app-password"
  }
}
```

## 🐳 Docker Support

The project includes Dockerfile for containerization:

```bash
# Build Docker image
docker build -t clean-architecture-api .

# Run container
docker run -p 8080:80 clean-architecture-api
```

## 📊 Logging

The application uses Serilog for structured logging:
- **Console Output**: Development logging
- **SQL Server**: Production logging to `SystemLogs` table
- **Configurable Log Levels**: Different levels for different namespaces

## 🌐 Localization

Supports multiple languages:
- **English (en-US)** - Default
- **Arabic (ar-EG)** - Supported

## 🧪 Testing

The project structure supports unit testing with:
- **CQRS Commands/Queries** - Easy to test business logic
- **Repository Pattern** - Mockable data access
- **Service Layer** - Testable application services

## 📝 Development Guidelines

### Code Organization
- **Feature-based Structure**: Each feature has its own folder with commands, queries, handlers, and validators
- **CQRS Pattern**: Clear separation between read and write operations
- **Dependency Injection**: All dependencies registered in respective modules
- **Pipeline Behaviors**: Cross-cutting concerns handled via MediatR behaviors

### Best Practices
- **Async/Await**: All I/O operations are asynchronous
- **Validation**: Input validation using FluentValidation
- **Error Handling**: Centralized error handling middleware
- **Response Wrapping**: Consistent API response format
- **Caching**: Implemented for frequently accessed data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions, please create an issue in the repository or contact the development team.

---

**Built with ❤️ using Clean Architecture principles and .NET 9.0**
