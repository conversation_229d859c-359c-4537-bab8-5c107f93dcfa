﻿using FluentValidation;
using Graduation.Core.Features.ApplicationUser.Commands.Models;
using Graduation.Core.Resources;
using Graduation.Service.Abstract;
using Microsoft.Extensions.Localization;

namespace Graduation.Core.Features.ApplicationUser.Commands.Validatiors
{
    public class EditUserValidator : AbstractValidator<EditUserCommand>
    {
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;
        private readonly IApplicationUserService _applicationUserService;

        #region Constructors
        public EditUserValidator(IStringLocalizer<SharedResources> sharedLocalizer, IApplicationUserService applicationUserService)
        {
            _sharedLocalizer = sharedLocalizer;
            this._applicationUserService = applicationUserService;
            ApplyValidationsRules();
            ApplyCustomValidationsRules();
        }
        #endregion

        #region Handle Functions
        public void ApplyValidationsRules()
        {
            RuleFor(x => x.FullName)
                 .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                 .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required])
                 .MaximumLength(100).WithMessage(_sharedLocalizer[SharedResourcesKeys.MaxLengthis100]);

            RuleFor(x => x.UserName)
                .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required])
                .MaximumLength(100).WithMessage(_sharedLocalizer[SharedResourcesKeys.MaxLengthis100]);

            RuleFor(x => x.Email)
                 .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                 .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required]);
        }

        public void ApplyCustomValidationsRules()
        {

            RuleFor(x => x.Id)
                .MustAsync(async (Key, CancellationToken) => await _applicationUserService.IsUserExist(Key))
                .WithMessage(_sharedLocalizer[SharedResourcesKeys.UserIsNotFound]);

        }

        #endregion
    }
}