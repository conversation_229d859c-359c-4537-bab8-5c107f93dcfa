﻿using FluentValidation;
using Graduation.Core.Features.Authorization.Commands.Models;
using Graduation.Core.Resources;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Graduation.Core.Features.Authorization.Commands.Validators
{
    public class UpdateUserRolesVaildators: AbstractValidator<UpdateUserRolesCommand>
    {

        #region Fields

        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;

        #endregion
        #region Constructors
        public UpdateUserRolesVaildators(
                                   IStringLocalizer<SharedResources> sharedLocalizer)
        {

            _sharedLocalizer = sharedLocalizer;
            ApplyValidationsRules();
            ApplyCustomValidationsRules();
        }
        #endregion
        #region  Functions
        public void ApplyValidationsRules()
        {
            RuleFor(x => x.UserId)
                 .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                 .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required]);
            RuleFor(x => x.Roles)
                 .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                 .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required]);
        }
        public void ApplyCustomValidationsRules()
        {

        }
        #endregion
    }
}
