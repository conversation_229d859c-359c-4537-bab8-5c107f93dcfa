﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Graduation.Data.Entities.Identity;
using Graduation.Data.Requests;
using Graduation.Data.Results;
using Graduation.Service.Abstract;
using System.Data;
using System.Security.Claims;
using Graduation.Data.Results;
using Graduation.Infrustructure.UnitOfWorks;

namespace Graduation.Service.Services
{

    public class AuthorizationService : IAuthorizationService
    {
        #region Fields
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly UserManager<AppUser> _userManager;
        private readonly IUnitOfWork _unitOfWork;

        // private readonly ApplicationDBContext _dbContext;
        #endregion
        #region Constructors
        public AuthorizationService(RoleManager<IdentityRole> roleManager,
                                    UserManager<AppUser> userManager,
                                    IUnitOfWork unitOfWork
                                 )
        {
            _roleManager = roleManager;
            _userManager = userManager;
            this._unitOfWork = unitOfWork;
            //  _dbContext = dbContext;
        }


        #endregion
        #region handle Functions
        public async Task<string> AddRoleAsync(string roleName)
        {
            var identityRole = new IdentityRole();
            identityRole.Name = roleName;
            var result = await _roleManager.CreateAsync(identityRole);
            if (result.Succeeded)
                return "Success";
            return "Failed";
        }




        public async Task<bool> IsRoleExistByName(string roleName)
        {
            return await _roleManager.RoleExistsAsync(roleName);
        }
        public async Task<string> EditRoleAsync(EditRoleRequest request)
        {
            //check role is exist or not
            var role = await _roleManager.FindByIdAsync(request.Id);
            if (role == null)
                return "notFound";
            role.Name = request.Name;
            var result = await _roleManager.UpdateAsync(role);
            if (result.Succeeded) return "Success";
            var errors = string.Join("-", result.Errors);
            return errors;
        }

        public async Task<string> DeleteRoleAsync(string roleId)
        {
            var role = await _roleManager.FindByIdAsync(roleId);
            if (role == null) return "NotFound";
            //Chech if user has this role or not
            var users = await _userManager.GetUsersInRoleAsync(role.Name);
            //return exception 
            if (users.Any()) return "Used";
            //delete
            var result = await _roleManager.DeleteAsync(role);
            //success
            if (result.Succeeded) return "Success";
            //problem
            var errors = string.Join("-", result.Errors);
            return errors;
        }

        public async Task<List<IdentityRole>> GetRolesList()
        {
            return await _roleManager.Roles.ToListAsync();
        }

        public async Task<IdentityRole> GetRoleById(string id)
        {
            return await _roleManager.FindByIdAsync(id);
        }

        public async Task<List<string>> GetUserRoles(AppUser user)
        {
           
            var UserRoles = await _userManager.GetRolesAsync(user);

            return UserRoles.ToList();
        }

        public async Task<string> UpdateUserRoles(string userId , List<string> roles)
        {
            await  _unitOfWork.BeginTransactionAsync();
            try
            {
                //Get User
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return "UserIsNull";
                }
                //get user Old Roles
                var userRoles = await _userManager.GetRolesAsync(user);
                //Delete OldRoles
                var removeResult = await _userManager.RemoveFromRolesAsync(user, userRoles);
                if (!removeResult.Succeeded)
                    return "FailedToRemoveOldRoles";
             
                //Add the Roles HasRole=True
                var addRolesresult = await _userManager.AddToRolesAsync(user, roles);
                if (!addRolesresult.Succeeded)
                    return "FailedToAddNewRoles";
                await _unitOfWork.CommitTransactionAsync();
                //return Result
                return "Success";
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return "FailedToUpdateUserRoles";
            }
        }
        public async Task<List<Claim>> ManageUserClaimData(AppUser user)
        {
       
            //Get USer Claims
            var userClaims = await _userManager.GetClaimsAsync(user); //edit
          
            return userClaims.ToList();
        }

        public async Task<string> UpdateUserClaims( string userId , List<Claim> claims)
        {
             await _unitOfWork.BeginTransactionAsync();
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return "UserIsNull";
                }
                //remove old Claims
                var userClaims = await _userManager.GetClaimsAsync(user);
                var removeClaimsResult = await _userManager.RemoveClaimsAsync(user, userClaims);
                if (!removeClaimsResult.Succeeded)
                    return "FailedToRemoveOldClaims";

                var addUserClaimResult = await _userManager.AddClaimsAsync(user, claims);
                if (!addUserClaimResult.Succeeded)
                    return "FailedToAddNewClaims";

                await _unitOfWork.CommitTransactionAsync();
                return "Success";
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return "FailedToUpdateClaims";
            }
        }
        #endregion
    }
}
