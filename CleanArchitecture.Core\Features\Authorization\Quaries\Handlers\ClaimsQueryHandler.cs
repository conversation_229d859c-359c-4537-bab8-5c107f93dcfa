﻿using Graduation.Core.Bases;
using Graduation.Core.Features.Authorization.Quaries.Models;
using Graduation.Core.Resources;
using Graduation.Data.Entities.Identity;
using Graduation.Data.Results;
using Graduation.Service.Abstract;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using System.Security.Claims;

namespace Graduation.Core.Features.Authorization.Quaries.Handlers
{
    public class ClaimsQueryHandler : ResponseHandler,
        IRequestHandler<GetUserClaimsQuery, Response<List<Claim>>>
    {
        #region Fileds
        private readonly IAuthorizationService _authorizationService;
        private readonly UserManager<AppUser> _userManager;
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;
        
        #endregion
        #region Constructors
        public ClaimsQueryHandler(
                                  IAuthorizationService authorizationService,
                                  UserManager<AppUser> userManager,
                                  IStringLocalizer<SharedResources> sharedLocalizer) : base(sharedLocalizer)
        {
            _authorizationService = authorizationService;
            _userManager = userManager;
            _sharedLocalizer = sharedLocalizer;
        }
        #endregion
        #region Handle Functions
        public async Task<Response<List<Claim>>> Handle(GetUserClaimsQuery request, CancellationToken cancellationToken)
        {
            var user = await _userManager.FindByIdAsync(request.UserId);
            if (user==null) return NotFound<List<Claim>>(_sharedLocalizer[SharedResourcesKeys.UserIsNotFound]);
            var result = await _authorizationService.ManageUserClaimData(user);
            return Success(result);
        }
        #endregion
    }
}
