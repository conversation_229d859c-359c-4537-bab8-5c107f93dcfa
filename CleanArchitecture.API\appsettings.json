{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "*******"}, "jwtSettings": {"Secret": "CleanArchitectureKey59848hjhui", "Issuer": "CleanArchitecture", "Audience": "WebSite", "ValidateAudience": false, "ValidateIssuer": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "AccessTokenExpireDate": 1, "RefreshTokenExpireDate": 20}, "MailSettings": {"EmailFrom": "*******", "SmtpPass": "********", "SmtpHost": "********", "SmtpPort": 587, "SmtpUser": "********", "DisplayName": "*********"}, "Serilog": {"Using": ["Serilog.Sinks.MSSqlServer", "Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "override": {"Microsoft": "Error"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "MSSqlServer", "Args": {"ConnectionString": "*********", "TableName": "SystemLogs", "autoCreateSqlTable": true}}]}}