# Clean Architecture Project - Technical Documentation

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Project Structure](#project-structure)
3. [Dependencies and Packages](#dependencies-and-packages)
4. [Database Design](#database-design)
5. [API Documentation](#api-documentation)
6. [Authentication & Authorization](#authentication--authorization)
7. [CQRS Implementation](#cqrs-implementation)
8. [Pipeline Behaviors](#pipeline-behaviors)
9. [Configuration Management](#configuration-management)
10. [Logging Strategy](#logging-strategy)
11. [Error Handling](#error-handling)
12. [Localization](#localization)
13. [Testing Strategy](#testing-strategy)
14. [Deployment](#deployment)

## Architecture Overview

This project implements Clean Architecture principles with the following layers:

### Layer Dependencies
```
API Layer (CleanArchitecture.API)
    ↓
Core Layer (CleanArchitecture.Core)
    ↓
Service Layer (CleanArchitecture.Service)
    ↓
Infrastructure Layer (CleanArchitecture.Infrastructure)
    ↓
Data Layer (CleanArchitecture.Data)
```

### Key Architectural Patterns
- **Clean Architecture**: Separation of concerns with dependency inversion
- **CQRS (Command Query Responsibility Segregation)**: Separate read and write operations
- **Repository Pattern**: Abstract data access layer
- **Unit of Work**: Transaction management
- **Specification Pattern**: Reusable query logic
- **Pipeline Behaviors**: Cross-cutting concerns

## Project Structure

### CleanArchitecture.API (Presentation Layer)
```
CleanArchitecture.API/
├── Controllers/
│   ├── AppControllerBase.cs          # Base controller with common functionality
│   ├── ApplicationUserController.cs  # User management endpoints
│   ├── AuthenticationController.cs   # Authentication endpoints
│   └── AuthorizationController.cs    # Authorization endpoints
├── Extensions/
│   └── ApplicationDependenciesExtensions.cs  # DI configuration
├── Properties/
│   └── launchSettings.json          # Launch configuration
├── appsettings.json                 # Application configuration
├── appsettings.Development.json     # Development configuration
├── Program.cs                       # Application entry point
└── Dockerfile                       # Container configuration
```

### CleanArchitecture.Core (Application Layer)
```
CleanArchitecture.Core/
├── Features/
│   ├── ApplicationUser/             # User management feature
│   │   ├── Commands/               # Write operations
│   │   ├── Queries/                # Read operations
│   │   └── Mapping/                # AutoMapper profiles
│   ├── Authentication/             # Authentication feature
│   │   ├── Commands/
│   │   └── Queries/
│   └── Authorization/              # Authorization feature
│       ├── Commands/
│       └── Queries/
├── Behaviors/                      # Pipeline behaviors
│   ├── CachingBehavior.cs
│   ├── LoggingBehavior.cs
│   └── ValidationBehavior.cs
├── Bases/                          # Base classes and contracts
│   ├── Contracts/
│   ├── Response.cs
│   └── ResponseHandler.cs
├── MiddleWares/                    # Custom middleware
│   ├── DbPreProcessMiddleware.cs
│   └── ErrorHandlerMiddleware.cs
├── Resources/                      # Localization resources
│   ├── SharedResources.cs
│   ├── SharedResourcesKeys.cs
│   ├── SharedResources.en.resx
│   └── SharedResources.ar.resx
└── Warppars/                       # Wrapper classes
    └── PaginatedResult.cs
```

### CleanArchitecture.Data (Domain Layer)
```
CleanArchitecture.Data/
├── Entities/
│   ├── BaseModel.cs                # Base entity class
│   └── Identity/
│       ├── AppUser.cs              # User entity
│       └── UserRefreshToken.cs     # Refresh token entity
├── Enums/
│   └── OrderByOrdring.cs           # Sorting enums
├── Results/
│   ├── JwtAuthResult.cs            # JWT response model
│   └── RefreshToken.cs             # Refresh token model
├── Settings/
│   ├── EmailSettings.cs            # Email configuration
│   └── JwtSettings.cs              # JWT configuration
└── Utilities/
    └── Roles.cs                    # Role constants
```

### CleanArchitecture.Infrastructure (Infrastructure Layer)
```
CleanArchitecture.Infrastructure/
├── Abstract/
│   └── IGenericRepository.cs       # Repository interface
├── Context/
│   ├── ApplicationDbContext.cs     # EF Core context
│   └── Migrations/                 # Database migrations
├── Repositories/
│   └── GenericRepository.cs        # Repository implementation
├── Specifications/
│   ├── Specification.cs            # Base specification
│   └── SpecificaionQueryBuilder.cs # Query builder
└── UnitOfWorks/
    ├── IUnitOfWork.cs              # Unit of work interface
    └── UnitOfWork.cs               # Unit of work implementation
```

### CleanArchitecture.Service (Service Layer)
```
CleanArchitecture.Service/
├── Abstract/
│   ├── IApplicationUserService.cs
│   ├── IAuthenticationService.cs
│   ├── IAuthorizationService.cs
│   └── IEmailService.cs
├── Services/
│   ├── ApplicationUserService.cs
│   ├── AuthenticationService.cs
│   ├── AuthorizationService.cs
│   └── EmailService.cs
└── ModuleServiceDependences.cs     # Service DI configuration
```

## Dependencies and Packages

### API Layer Dependencies
```xml
<PackageReference Include="MediatR" Version="13.0.0" />
<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.7" />
<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
<PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
```

### Core Layer Dependencies
```xml
<PackageReference Include="AutoMapper" Version="15.0.1" />
<PackageReference Include="FluentValidation" Version="12.0.0" />
<PackageReference Include="MediatR" Version="13.0.0" />
<PackageReference Include="Serilog" Version="4.3.0" />
```

### Infrastructure Layer Dependencies
```xml
<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.8" />
<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.8" />
<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.8" />
<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.14.0" />
```

### Service Layer Dependencies
```xml
<PackageReference Include="MailKit" Version="4.13.0" />
```

## Database Design

### Entity Framework Core Configuration
- **Provider**: SQL Server
- **Identity**: ASP.NET Core Identity with custom AppUser
- **Migrations**: Code-first approach with automatic migrations

### Entity Models

#### AppUser (Identity User)
```csharp
public class AppUser : IdentityUser
{
    public string FullName { get; set; }
    public string? Address { get; set; }
    public string? Country { get; set; }
    public virtual ICollection<UserRefreshToken> UserRefreshTokens { get; set; }
}
```

#### UserRefreshToken
```csharp
public class UserRefreshToken
{
    public int Id { get; set; }
    public string UserId { get; set; }
    public string Token { get; set; }
    public DateTime ExpiresOn { get; set; }
    public bool IsRevoked { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime? RevokedOn { get; set; }
}
```

### Database Context
```csharp
public class ApplicationDbContext : IdentityDbContext<AppUser>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }
    
    public DbSet<UserRefreshToken> UserRefreshTokens { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(modelBuilder);
    }
}
```

## API Documentation

### Base Controller
All controllers inherit from `AppControllerBase` which provides:
- MediatR integration
- Standardized response handling
- Common functionality

### Authentication Endpoints

#### POST /Authentication/SignIn
- **Purpose**: User authentication
- **Input**: Email, Password
- **Output**: JWT token and refresh token
- **Validation**: Required fields, email format

#### POST /Authentication/RefreshToken
- **Purpose**: Refresh expired JWT token
- **Input**: Refresh token
- **Output**: New JWT token and refresh token

#### GET /Authentication/ValidateToken
- **Purpose**: Validate JWT token
- **Input**: JWT token
- **Output**: Token validation result

#### GET /Authentication/ConfirmEmail
- **Purpose**: Confirm user email address
- **Input**: User ID, confirmation token
- **Output**: Confirmation result

#### POST /Authentication/SendResetPasswordCode
- **Purpose**: Send password reset code via email
- **Input**: Email address
- **Output**: Success/failure response

#### GET /Authentication/ConfirmResetPasswordCode
- **Purpose**: Validate password reset code
- **Input**: Email, reset code
- **Output**: Validation result

#### POST /Authentication/ResetPassword
- **Purpose**: Reset user password
- **Input**: Email, reset code, new password
- **Output**: Reset result

### Authorization Endpoints

#### GET /Authorization/GetUserRoles
- **Purpose**: Get roles for specific user
- **Input**: User ID
- **Output**: List of user roles

#### POST /Authorization/UpdateUserRoles
- **Purpose**: Update user roles
- **Input**: User ID, list of roles
- **Output**: Update result

#### GET /Authorization/GetRoles
- **Purpose**: Get all available roles
- **Output**: List of all roles

#### POST /Authorization/AddRole
- **Purpose**: Create new role
- **Input**: Role name
- **Output**: Creation result

#### PUT /Authorization/EditRole
- **Purpose**: Update existing role
- **Input**: Role ID, new role name
- **Output**: Update result

#### DELETE /Authorization/DeleteRole
- **Purpose**: Delete role
- **Input**: Role ID
- **Output**: Deletion result

### User Management Endpoints

#### GET /ApplicationUser/GetUsers
- **Purpose**: Get paginated list of users
- **Input**: Page number, page size, search criteria
- **Output**: Paginated user list

#### POST /ApplicationUser/AddUser
- **Purpose**: Create new user
- **Input**: User details (name, email, password, etc.)
- **Output**: Creation result

#### PUT /ApplicationUser/EditUser
- **Purpose**: Update user information
- **Input**: User ID, updated user details
- **Output**: Update result

#### DELETE /ApplicationUser/DeleteUser
- **Purpose**: Delete user
- **Input**: User ID
- **Output**: Deletion result

#### POST /ApplicationUser/ChangePassword
- **Purpose**: Change user password
- **Input**: User ID, current password, new password
- **Output**: Change result

## Authentication & Authorization

### JWT Configuration
```json
{
  "jwtSettings": {
    "Secret": "CleanArchitectureKey59848hjhui",
    "Issuer": "CleanArchitecture",
    "Audience": "WebSite",
    "ValidateAudience": false,
    "ValidateIssuer": true,
    "ValidateLifetime": true,
    "ValidateIssuerSigningKey": true,
    "AccessTokenExpireDate": 1,
    "RefreshTokenExpireDate": 20
  }
}
```

### Role-Based Authorization
- **Admin**: Full system access
- **User**: Basic user access
- **Organizer**: Event management access

### Claims-Based Authorization
- Custom claims for fine-grained permissions
- Role-based access control
- Resource-based authorization

## CQRS Implementation

### Command Pattern
Commands represent write operations:
```csharp
public class SignInCommand : IRequest<Response<JwtAuthResult>>
{
    public string Email { get; set; }
    public string Password { get; set; }
}
```

### Query Pattern
Queries represent read operations:
```csharp
public class GetUserPaginationQuery : IRequest<Response<PaginatedResult<GetUserPaginationResponse>>>
{
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public string? Search { get; set; }
}
```

### Handler Implementation
```csharp
public class SignInCommandHandler : IRequestHandler<SignInCommand, Response<JwtAuthResult>>
{
    public async Task<Response<JwtAuthResult>> Handle(SignInCommand request, CancellationToken cancellationToken)
    {
        // Implementation
    }
}
```

## Pipeline Behaviors

### Validation Behavior
- Automatic validation of requests using FluentValidation
- Returns validation errors before reaching handlers
- Implements `IPipelineBehavior<TRequest, TResponse>`

### Caching Behavior
- Implements caching for cacheable requests
- Uses `ICacheableRequest` interface to identify cacheable requests
- Configurable cache duration

### Logging Behavior
- Logs all requests and responses
- Uses Serilog for structured logging
- Includes request/response details

## Configuration Management

### Application Settings
- **appsettings.json**: Production configuration
- **appsettings.Development.json**: Development overrides
- **User Secrets**: Sensitive data (connection strings, API keys)

### Configuration Classes
```csharp
public class JwtSettings
{
    public string Secret { get; set; }
    public string Issuer { get; set; }
    public string Audience { get; set; }
    public bool ValidateAudience { get; set; }
    public bool ValidateIssuer { get; set; }
    public bool ValidateLifeTime { get; set; }
    public bool ValidateIssuerSigningKey { get; set; }
    public int AccessTokenExpireDate { get; set; }
    public int RefreshTokenExpireDate { get; set; }
}
```

## Logging Strategy

### Serilog Configuration
```json
{
  "Serilog": {
    "Using": ["Serilog.Sinks.MSSqlServer", "Serilog.Sinks.Console"],
    "MinimumLevel": {
      "Default": "Information",
      "override": {
        "Microsoft": "Error"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "MSSqlServer",
        "Args": {
          "ConnectionString": "connection-string",
          "TableName": "SystemLogs",
          "autoCreateSqlTable": true
        }
      }
    ]
  }
}
```

### Logging Levels
- **Information**: General application flow
- **Warning**: Potential issues
- **Error**: Application errors
- **Debug**: Detailed debugging information

## Error Handling

### Global Error Handler
```csharp
public class ErrorHandlerMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        try
        {
            await next(context);
        }
        catch (Exception ex)
        {
            // Log error and return standardized response
        }
    }
}
```

### Response Format
```csharp
public class Response<T>
{
    public bool Succeeded { get; set; }
    public string Message { get; set; }
    public List<string> Errors { get; set; }
    public T Data { get; set; }
    public HttpStatusCode StatusCode { get; set; }
    public object Meta { get; set; }
}
```

## Localization

### Supported Languages
- **English (en-US)**: Default language
- **Arabic (ar-EG)**: Right-to-left support

### Resource Files
- **SharedResources.en.resx**: English translations
- **SharedResources.ar.resx**: Arabic translations
- **SharedResourcesKeys.cs**: Resource key constants

### Implementation
```csharp
services.AddLocalization(opt =>
{
    opt.ResourcesPath = "";
});

services.Configure<RequestLocalizationOptions>(options =>
{
    List<CultureInfo> supportedCultures = new List<CultureInfo>
    {
        new CultureInfo("en-US"),
        new CultureInfo("ar-EG")
    };
    
    options.DefaultRequestCulture = new RequestCulture("en-US");
    options.SupportedCultures = supportedCultures;
    options.SupportedUICultures = supportedCultures;
});
```

## Testing Strategy

### Unit Testing
- **Command/Query Handlers**: Test business logic
- **Services**: Test application services
- **Validators**: Test input validation
- **Repositories**: Test data access (with in-memory database)

### Integration Testing
- **API Endpoints**: Test complete request/response cycle
- **Database Operations**: Test with real database
- **Authentication Flow**: Test JWT token generation and validation

### Test Structure
```
Tests/
├── Unit/
│   ├── Commands/
│   ├── Queries/
│   ├── Services/
│   └── Validators/
├── Integration/
│   ├── Controllers/
│   └── Database/
└── TestUtilities/
    ├── TestDataBuilder/
    └── MockFactories/
```

## Deployment

### Docker Support
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["CleanArchitecture.API/CleanArchitecture.API.csproj", "CleanArchitecture.API/"]
# ... copy and restore packages
RUN dotnet build "CleanArchitecture.API/CleanArchitecture.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "CleanArchitecture.API/CleanArchitecture.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "CleanArchitecture.API.dll"]
```

### Environment Configuration
- **Development**: Local development with hot reload
- **Staging**: Pre-production testing environment
- **Production**: Live production environment

### Database Migrations
```bash
# Create migration
dotnet ef migrations add MigrationName --project CleanArchitecture.Infrastructure --startup-project CleanArchitecture.API

# Update database
dotnet ef database update --project CleanArchitecture.Infrastructure --startup-project CleanArchitecture.API
```

## Security Considerations

### Authentication Security
- **JWT Tokens**: Secure token generation and validation
- **Refresh Tokens**: Secure token refresh mechanism
- **Password Hashing**: ASP.NET Core Identity password hashing
- **Email Verification**: Required for account activation

### Authorization Security
- **Role-Based Access**: Proper role assignment and validation
- **Claims-Based Authorization**: Fine-grained permissions
- **Resource Protection**: Protected endpoints and resources

### Data Security
- **Connection Strings**: Stored in user secrets/secure configuration
- **Sensitive Data**: Properly encrypted and protected
- **SQL Injection**: Prevented by Entity Framework parameterized queries

## Performance Considerations

### Caching Strategy
- **Memory Caching**: For frequently accessed data
- **Response Caching**: For API responses
- **Database Query Optimization**: Efficient queries and indexing

### Database Optimization
- **Connection Pooling**: Efficient database connections
- **Query Optimization**: Optimized Entity Framework queries
- **Indexing**: Proper database indexing strategy

### API Performance
- **Async/Await**: Non-blocking I/O operations
- **Pagination**: Efficient data retrieval
- **Response Compression**: Reduced payload size

---

This documentation provides a comprehensive overview of the Clean Architecture project's technical implementation, patterns, and best practices. For specific implementation details, refer to the source code and inline documentation.
