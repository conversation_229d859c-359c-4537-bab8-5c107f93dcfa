﻿using Graduation.Core.Bases;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Graduation.Core.Features.ApplicationUser.Queries.Models
{
    public class GetUserIdByEmailQuery: IRequest<Response<string>>
    {
        public string Email { get; set; }
        public GetUserIdByEmailQuery(string email)
        {
            Email = email;
        }
    }

}
