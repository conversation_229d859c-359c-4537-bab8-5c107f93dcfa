﻿using FluentValidation;
using Graduation.Core.Bases;
using Graduation.Core.Behaviors;
using Graduation.Core.Mapping.ApplicationUser;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace Graduation.Core
{
    public static class ModuleCoreDependences
    {
        public static IServiceCollection CoreDependences(this IServiceCollection services)
        {
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));

            // With this line:
            services.AddAutoMapper(cfg => cfg.AddMaps(Assembly.GetExecutingAssembly()));
          
            // get validators
            services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
            //caching
            services.AddTransient(typeof(IPipelineBehavior<,>), typeof(CachingBehavior<,>));
            // 
            services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
            // Add pipeline behaviors
            services.AddTransient(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));

            return services;
        }
    }
}
