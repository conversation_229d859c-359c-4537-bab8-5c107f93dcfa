﻿
using Graduation.Data.Entities.Identity;
using Graduation.Data.Requests;
using Graduation.Data.Results;
using Microsoft.AspNetCore.Identity;
using System.Security.Claims;
namespace Graduation.Service.Abstract
{
    public interface IAuthorizationService
    {
        public Task<string> AddRoleAsync(string roleName);
        public Task<bool> IsRoleExistByName(string roleName);
        public Task<string> EditRoleAsync(EditRoleRequest request);
        public Task<string> DeleteRoleAsync(string roleId);
        public Task<List<IdentityRole>> GetRolesList();
        public Task<IdentityRole> GetRoleById(string id);
        public  Task<List<string>> GetUserRoles(AppUser user);
        public Task<string> UpdateUserRoles(string userId, List<string> roles);
        public Task<List<Claim>> ManageUserClaimData(AppUser user);
        public Task<string> UpdateUserClaims(string userId, List<Claim> claims);
    }
}
