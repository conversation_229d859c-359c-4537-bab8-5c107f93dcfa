﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Graduation.Core.Behaviors
{
    using Graduation.Core.Bases.Contracts;
    using MediatR;
    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.Caching.Memory;
    using System.Text.Json;

    public class CachingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
        where TRequest : IRequest<TResponse>
    {
        private readonly IMemoryCache _cache;

        public CachingBehavior(IMemoryCache cache)
        {
            _cache = cache;
        }

        public async Task<TResponse> Handle(
            TRequest request,
            RequestHandlerDelegate<TResponse> next,
            CancellationToken cancellationToken)
        {

            if (request is not ICacheableRequest cacheableRequest)
            {
                // ❌ Not cacheable → just run
                return await next();
            }

            var cacheKey = $"{typeof(TRequest).FullName}:{JsonSerializer.Serialize(request)}";

            // Try get from cache
            if (_cache.TryGetValue(cacheKey, out TResponse cachedResponse))
            {
                return cachedResponse;
            }

            // Execute request handler
            var response = await next();

            
            var expiration = cacheableRequest.Expiration ?? TimeSpan.FromMinutes(5);

            _cache.Set(cacheKey, response, expiration);

            return response;
        }
  
    }

}
