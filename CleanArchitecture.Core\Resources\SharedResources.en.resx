﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Created" xml:space="preserve">
    <value>Successfully created</value>
  </data>
  <data name="NotFound" xml:space="preserve">
    <value>No data found</value>
  </data>
  <data name="TryToRegisterAgain" xml:space="preserve">
    <value>Please try registering again</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>This field is required</value>
  </data>
  <data name="Deleted" xml:space="preserve">
    <value>Successfully deleted</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Operation completed successfully</value>
  </data>
  <data name="NotEmpty" xml:space="preserve">
    <value>This field cannot be empty</value>
  </data>
  <data name="Updated" xml:space="preserve">
    <value>Successfully updated</value>
  </data>
  <data name="UnAuthorized" xml:space="preserve">
    <value>You are not authorized to perform this action</value>
  </data>
  <data name="BadRequest" xml:space="preserve">
    <value>Invalid request. Please check your input</value>
  </data>
  <data name="UnprocessableEntity" xml:space="preserve">
    <value>Unable to process your request. Please try again</value>
  </data>
  <data name="MaxLengthis100" xml:space="preserve">
    <value>Maximum length allowed is 100 characters</value>
  </data>
  <data name="IsExist" xml:space="preserve">
    <value>Already exists</value>
  </data>
  <data name="IsNotExist" xml:space="preserve">
    <value>Not found</value>
  </data>
  <data name="DepartmementId" xml:space="preserve">
    <value>Department ID is required</value>
  </data>
  <data name="PasswordNotEqualConfirmPass" xml:space="preserve">
    <value>Password and confirm password do not match</value>
  </data>
  <data name="EmailIsExist" xml:space="preserve">
    <value>This email address is already registered</value>
  </data>
  <data name="UserNameIsExist" xml:space="preserve">
    <value>This username is already taken</value>
  </data>
  <data name="FaildToAddUser" xml:space="preserve">
    <value>Unable to create user account. Please try again</value>
  </data>
  <data name="UpdateFailed" xml:space="preserve">
    <value>Unable to update. Please try again</value>
  </data>
  <data name="DeletedFailed" xml:space="preserve">
    <value>Unable to delete. Please try again</value>
  </data>
  <data name="ChangePassFailed" xml:space="preserve">
    <value>Unable to change password. Please try again</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>Username is required</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password is required</value>
  </data>
  <data name="UserNameIsNotExist" xml:space="preserve">
    <value>Username not found</value>
  </data>
  <data name="PasswordNotCorrect" xml:space="preserve">
    <value>Incorrect password</value>
  </data>
  <data name="AlgorithmIsWrong" xml:space="preserve">
    <value>Authentication error. Please try again</value>
  </data>
  <data name="TokenIsNotExpired" xml:space="preserve">
    <value>Session is still active</value>
  </data>
  <data name="RefreshTokenIsNotFound" xml:space="preserve">
    <value>Session expired. Please log in again</value>
  </data>
  <data name="RefreshTokenIsExpired" xml:space="preserve">
    <value>Session expired. Please log in again</value>
  </data>
  <data name="TokenIsExpired" xml:space="preserve">
    <value>Session expired. Please log in again</value>
  </data>
  <data name="AddFailed" xml:space="preserve">
    <value>Unable to add. Please try again</value>
  </data>
  <data name="RoleNotExist" xml:space="preserve">
    <value>Role not found</value>
  </data>
  <data name="RoleIsUsed" xml:space="preserve">
    <value>This role is currently assigned to users</value>
  </data>
  <data name="UserIsNotFound" xml:space="preserve">
    <value>User account not found</value>
  </data>
  <data name="FailedToRemoveOldRoles" xml:space="preserve">
    <value>Unable to remove previous roles. Please try again</value>
  </data>
  <data name="FailedToUpdateUserRoles" xml:space="preserve">
    <value>Unable to update user roles. Please try again</value>
  </data>
  <data name="FailedToAddNewRoles" xml:space="preserve">
    <value>Unable to assign new roles. Please try again</value>
  </data>
  <data name="FailedToUpdateClaims" xml:space="preserve">
    <value>Unable to update permissions. Please try again</value>
  </data>
  <data name="FailedToAddNewClaims" xml:space="preserve">
    <value>Unable to add new permissions. Please try again</value>
  </data>
  <data name="FailedToRemoveOldClaims" xml:space="preserve">
    <value>Unable to remove previous permissions. Please try again</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email address is required</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>Message is required</value>
  </data>
  <data name="SendEmailFailed" xml:space="preserve">
    <value>Unable to send email. Please try again</value>
  </data>
  <data name="EmailNotConfirmed" xml:space="preserve">
    <value>Please confirm your email address</value>
  </data>
  <data name="ErrorWhenConfirmEmail" xml:space="preserve">
    <value>Unable to confirm email. Please try again</value>
  </data>
  <data name="ConfirmEmailDone" xml:space="preserve">
    <value>Email address confirmed successfully</value>
  </data>
  <data name="TryAgainInAnotherTime" xml:space="preserve">
    <value>Please try again later</value>
  </data>
  <data name="InvaildCode" xml:space="preserve">
    <value>Invalid verification code</value>
  </data>
  <data name="NoImage" xml:space="preserve">
    <value>No image provided</value>
  </data>
  <data name="FailedToUploadImage" xml:space="preserve">
    <value>Unable to upload image. Please try again</value>
  </data>
</root>