﻿using FluentValidation;
using Microsoft.Extensions.Localization;
using Graduation.Core.Features.ApplicationUser.Commands.Models;
using Graduation.Core.Resources;

namespace Graduation.Core.Features.ApplicationUser.Commands.Validatiors
{
    public class ChangeUserPasswordValidator : AbstractValidator<ChangeUserPasswordCommand>
    {
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;

        #region Constructors
        public ChangeUserPasswordValidator(IStringLocalizer<SharedResources> sharedLocalizer)
        {
            _sharedLocalizer = sharedLocalizer;
            ApplyValidationsRules();
            ApplyCustomValidationsRules();
        }
        #endregion

        #region Handle Functions
        public void ApplyValidationsRules()
        {

            RuleFor(x => x.Id)
                .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required]);

            RuleFor(x => x.CurrentPassword)
                 .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                 .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required]);
            RuleFor(x => x.NewPassword)
                 .NotEmpty().WithMessage(_sharedLocalizer[SharedResourcesKeys.NotEmpty])
                 .NotNull().WithMessage(_sharedLocalizer[SharedResourcesKeys.Required]);
            RuleFor(x => x.ConfirmPassword)
                 .Equal(x => x.NewPassword).WithMessage(_sharedLocalizer[SharedResourcesKeys.PasswordNotEqualConfirmPass]);

        }

        public void ApplyCustomValidationsRules()
        {

        }

        #endregion
    }
}