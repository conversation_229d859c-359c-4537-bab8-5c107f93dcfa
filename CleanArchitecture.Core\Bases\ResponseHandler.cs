﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Graduation.Core.Resources;

namespace Graduation.Core.Bases
{
    public class ResponseHandler
    {
        private readonly IStringLocalizer<SharedResources> _sharedLocalizer;

        public ResponseHandler()
        {

        }

        public ResponseHandler(IStringLocalizer<SharedResources> sharedLocalizer)
        {
            _sharedLocalizer = sharedLocalizer;
        }
        public Response<T> Deleted<T>()
        {
            return new Response<T>()
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                Succeeded = true,
                Message = _sharedLocalizer?[SharedResourcesKeys.Deleted] ?? "Deleted Successfully"
            };
        }
        public Response<T> Success<T>(T entity, object Meta = null)
        {
            return new Response<T>()
            {
                Data = entity,
                StatusCode = System.Net.HttpStatusCode.OK,
                Succeeded = true,
                Message = _sharedLocalizer?[SharedResourcesKeys.Success] ?? "Added Successfully",
                Meta = Meta
            };
        }
        public Response<T> Unauthorized<T>( string message = null)
        {
            return new Response<T>()
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                Succeeded = true,
                Message = message ?? _sharedLocalizer?[SharedResourcesKeys.UnAuthorized] ?? "UnAuthorized"
            };
        }
        public Response<T> BadRequest<T>(string Message = null)
        {
            return new Response<T>()
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                Succeeded = false,
                Message = Message ?? _sharedLocalizer?[SharedResourcesKeys.BadRequest] ?? "Bad Request"
            };
        }
        public Response<T> BadRequest<T>( List<string> ErrorsList,string Message = null)
        {
            return new Response<T>()
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                Succeeded = false,
                Message = Message ?? _sharedLocalizer?[SharedResourcesKeys.BadRequest] ?? "Bad Request",
                Errors = ErrorsList

            };
        }
        public Response<T> NotFound<T>(string message = null)
        {
            return new Response<T>()
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                Succeeded = false,
                Message = message ?? _sharedLocalizer?[SharedResourcesKeys.NotFound] ?? "Not Found"
            };
        }

        public Response<T> Created<T>(T entity, object Meta = null)
        {
            return new Response<T>()
            {
                Data = entity,
                StatusCode = System.Net.HttpStatusCode.Created,
                Succeeded = true,
                Message = _sharedLocalizer?[SharedResourcesKeys.Created] ?? "Created",
                Meta = Meta
            };
        }
    }


}
