﻿using Graduation.Core.Bases;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Graduation.Core.Features.ApplicationUser.Queries.Models
{
    public class GetUserIdByUserNameQuery : IRequest<Response<string>>
    {
        public string UserName { get; set; }
        public GetUserIdByUserNameQuery(string userName)
        {
            UserName = userName;
        }
    }
 
}
